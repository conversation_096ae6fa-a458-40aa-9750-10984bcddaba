
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提取的Markdown内容</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js"></script>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
img {
    max-width: 100%;
    max-height: 100vh;
    height: auto;
    width: auto;
    display: block;
    margin: 0 auto;
}
body {
    margin: 0;
    padding: 20px;
    font-family: Arial, sans-serif;
    padding-bottom: 270px; /* 为浮动按钮增加底部内边距，防止遮挡内容 (220px bottom + 30px height + 20px buffer) */
}
.immersive-translate-target-inner {
    color: blue;
}
td, th {
    text-align: center;
    vertical-align: middle;
}
/* 浮动按钮样式现在由内联样式提供，此处无需额外CSS，除非需要 hover 等伪类效果 */
/* 
#plugin-trigger-button:hover {
    background-color: #0056b3; // 示例：如果需要 hover 效果可以这样添加
}
*/
    </style>
</head>
<body>
    <div class="wrap center full svelte-au1olv hide" style="position: absolute; padding: 0px;"> <div class="svelte-1ed2p3z"> <span class="md svelte-8tpqd2 prose"><p>志的技术要求。  </p>
<h1>1.4 智能船舶附加标志</h1>
<p>1.4.1 根据申请，经 CCS 审图与检验，确认船舶在智能航行、智能船体、智能机舱、智能能效管理、智能货物管理、智能集成平台、远程控制和自主操作方面已符合本规范要求，可按下列方式授予如下智能船舶附加标志：  </p>
<p>i-Ship (Ai ,Ri, Nx, Hx, Mx, Ex, Cx, I)  </p>
<p>其中括号内的字母是智能船舶的功能标志，可根据船舶实际具有的功能授予，功能标志可根据技术的发展增加。  </p>
<p>1.4.2 功能标志的含义如下：  </p>
<p>Ai--自主操作功能标志，应满足本规范第 9 章的要求；  </p>
<p>Ri--远程控制功能标志，应满足本规范第 8 章的要求；  </p>
<p>Nx--智能航行功能标志，应满足本规范第 2 章的要求；  </p>
<p>Hx--智能船体功能标志，应满足本规范第 3 章的要求；  </p>
<p>Mx--智能机舱功能标志，应满足本规范第 4 章的要求；  </p>
<p>Ex--智能能效管理功能标志，应满足本规范第 5 章的要求；  </p>
<p>Cx--智能货物管理功能标志，应满足本规范第 6 章的要求；  </p>
<p>I--智能集成平台功能标志，应满足本规范第 7 章的要求；  </p>
<p>i--为数字 1，2，3，表示远程控制和自主操作的范围和程度。根据船舶的具体功能，只能选择一个对应的数字；  </p>
<p>x--可选功能补充标志，一个小写字母表示一个功能补充标志，一个功能标志可有多个功能补充标志，并用“,”分开，具体详见本规范第 2 章至第 7 章的要求。  </p>
<p>1.4.3 如果一个功能标志已涵盖另一个标志的功能，则不重复授予。功能标志可按下述原则组合：  </p>
<p>（1） Nx、Hx、Mx、Ex、Cx、I 可根据船舶实际具有的功能授予；  </p>
<p>（2） Ai 和 Ri 之间，根据船舶实际情况只能选一个；<br>（3） R1 可以和 Nx、Hx、Mm,a,p、Ex、Cx 同时授予；<br>（4） R2 可以和 Hx、Mm,a,p、Ex、Cx 同时授予；<br>（5） Ai 可以和 Hx、Mm,a,p、Ex、Cx 同时授予。  </p>
<p>1.4.4 对于从事疏浚作业的挖泥船，经申请，并经 CCS 审图和检验符合本规范第 10 章10.2 的规定，可授予智能疏浚功能标志 Dx。有关智能疏浚功能标志 Dx 含义及技术要求见表 1.4.4。  </p>
<p>1.4.5 对于从事科考任务的科考船，经申请，并经 CCS 审图和检验符合本规范第 10 章10.3 的规定，可授予智能科考功能标志 SRx。有关智能科考功能标志 SRx 含义及技术要求见表 1.4.4。  </p>
<p>智能作业功能标志 表 1.4.4  </p>
<table class="table table-striped table-bordered"><tbody><tr><td style="font-weight: bold; font-size: 20px;">智能作业功能 标志</td><td colspan="2" style="font-weight: bold; font-size: 20px;">功能标志含义说明</td><td style="font-weight: bold; font-size: 20px;">技术要求</td></tr><tr><td style="font-weight: normal;">Dx</td><td style="font-weight: normal;">智能疏浚</td><td style="font-weight: normal;">D—一表示疏浚船具有本规范第10章******** 规定 的智能疏浚基本功能; x一一补充功能标志，具体采用以下小写字母表示： a一一表示可实现本规范第10章********（1）规 定的一键疏浚功能; m一一表示疏浚设备实施视情维护；</td><td style="font-weight: normal;">本规范第10章 10.2</td></tr></tbody></table>  </span></div></div>

    <!-- 新的浮动按钮，使用用户提供的HTML和内联样式 -->
    <div id="plugin-trigger-button" style="
        position: fixed;
        bottom: 220px;
        right: 20px;
        width: 30px;
        height: 30px;
        background-color: #007bff;
        color: Yellow;
        border-radius: 50%;
        text-align: center;
        line-height: 30px;
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
        z-index: 9999;
    ">
        ~
    </div>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // KaTeX 渲染 (已存在)
            if (typeof renderMathInElement === "function") {
                renderMathInElement(document.body, {
                    delimiters: [
                        {left: "$$", right: "$$", display: true},
                        {left: "$", right: "$", display: false},
                        {left: "\[", right: "\]", display: true},
                        {left: "\(", right: "\)", display: false}
                    ]
                });
            }

            // 为新的浮动按钮 (#plugin-trigger-button) 添加点击事件处理
            const triggerButton = document.getElementById('plugin-trigger-button'); // ID 已更新
            if (triggerButton) {
                triggerButton.addEventListener('click', function() {
                    console.log("Plugin trigger button clicked, re-extracting current HTML content.");

                    // 1. 克隆当前页面的 body 节点
                    const bodyClone = document.body.cloneNode(true);

                    // 2. 从克隆的 body 中移除浮动按钮本身
                    const buttonInClone = bodyClone.querySelector('#plugin-trigger-button'); // ID 已更新
                    if (buttonInClone) {
                        buttonInClone.parentNode.removeChild(buttonInClone);
                    }
                    
                    let extractedBodyHtml = bodyClone.innerHTML;

                    // 3. 获取当前页面的 <head> 内容的 innerHTML
                    let headHtml = document.head.innerHTML;
                    
                    // 4. 构建新的完整HTML文档字符串
                    const newFullHtmlContentToDownload = `
<!DOCTYPE html>
<html lang="${document.documentElement.lang || 'zh-CN'}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>再次提取的内容 - ${new Date().toISOString().replace(/[T:.-]/g, '').substring(0,14)}</title>
    ${headHtml}
    <style> 
        /* 确保再次下载的文件中 body padding 正常，且如果按钮意外残留则隐藏 */
        body { padding-bottom: 20px !important; } 
        #plugin-trigger-button { display: none !important; } /* ID 已更新 */
    </style>
</head>
<body>
    ${extractedBodyHtml}
</body>
</html>
`;

                    // 5. 下载逻辑
                    const blob = new Blob([newFullHtmlContentToDownload], { type: "text/html;charset=utf-8" });
                    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                    const fileName = `re_extracted_content_${timestamp}.html`;
                    const link = document.createElement("a");
                    link.href = URL.createObjectURL(blob);
                    link.download = fileName;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    console.log("Content re-extracted and download initiated as " + fileName);
                });
            }
        });
    </script>
</body>
</html>
