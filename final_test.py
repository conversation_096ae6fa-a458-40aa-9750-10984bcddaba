#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

# 导入合并HTML.py中的函数
sys.path.append('.')
from 合并HTML import merge_html_files

def final_test():
    """最终测试HTML文件合并功能"""
    current_dir = os.getcwd()
    print(f"当前目录: {current_dir}")
    
    # 列出当前目录的HTML文件
    html_files = [f for f in os.listdir(current_dir) if f.endswith('.html') and f != 'merged.html']
    print(f"找到的HTML文件: {html_files}")
    
    if len(html_files) >= 2:
        print("开始合并HTML文件...")
        result = merge_html_files(current_dir, "merged.html")
        if result:
            print(f"✓ 合并成功! 输出文件: {result}")
            
            # 检查文件是否真的存在
            if os.path.exists(result):
                file_size = os.path.getsize(result)
                print(f"✓ 文件确实存在，大小: {file_size} 字节")
                
                # 检查文件开头
                with open(result, 'r', encoding='utf-8') as f:
                    first_lines = f.read(500)
                print("文件开头内容:")
                print(first_lines[:200] + "...")
            else:
                print("✗ 错误: 文件不存在!")
        else:
            print("✗ 合并失败!")
    else:
        print("需要至少2个HTML文件才能进行合并测试")

if __name__ == "__main__":
    final_test()
