# HTML文件合并工具使用说明

## 功能特点

这个工具可以将多个具有相同文件头样式的HTML文件合并成一个单独的文件，特别适用于合并类似 `1活着.html` 和 `2活着.html` 这样的文件。

### 主要功能

1. **智能头部样式提取**: 自动使用第一个文件的完整头部样式（包括CSS、JavaScript等）
2. **内容清理**: 自动移除可能导致显示问题的样式：
   - `hide` 类
   - `position: absolute` 样式
   - `display: none` 样式
   - `visibility: hidden` 样式
3. **文件分隔**: 在合并的内容之间添加清晰的分隔标记和分隔线
4. **自动排序**: 按文件名自然排序（支持数字排序）
5. **标题更新**: 自动更新合并文档的标题

## 使用方法

### 方法一：图形界面

1. 双击运行 `合并HTML.py`
2. 在弹出的窗口中选择包含HTML文件的目录
3. 点击"确认合并"按钮
4. 合并完成后会自动在浏览器中打开结果文件

### 方法二：命令行

```python
from 合并HTML import merge_html_files

# 合并当前目录下的HTML文件
result = merge_html_files('.', 'merged.html')
if result:
    print(f"合并成功: {result}")
```

## 输出文件结构

合并后的HTML文件具有以下结构：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- 第一个文件的完整头部样式 -->
    <title>合并文档 - 原标题</title>
    <!-- CSS和JavaScript资源 -->
</head>
<body>
    <!-- 开始: 1活着.html -->
    <!-- 第一个文件的内容（已清理问题样式） -->
    <!-- 结束: 1活着.html -->
    
    <hr style="margin: 20px 0; border: 1px solid #ccc;" />
    
    <!-- 开始: 2活着.html -->
    <!-- 第二个文件的内容（已清理问题样式） -->
    <!-- 结束: 2活着.html -->
</body>
</html>
```

## 注意事项

1. **文件要求**: 需要至少2个HTML文件才能进行合并
2. **文件排除**: 自动排除名为 `merged.html` 的文件，避免重复合并
3. **样式兼容**: 工具会自动清理可能导致显示问题的样式，确保内容正常显示
4. **编码支持**: 支持UTF-8编码，适合中文内容

## 技术特性

- 自动检测和移除隐藏元素的样式
- 保持原有的CSS和JavaScript功能
- 智能处理文件名排序（支持数字序号）
- 自动生成文件分隔标记便于识别内容来源

## 适用场景

- 合并分章节的电子书HTML文件
- 整合多个网页内容为单一文档
- 合并具有相同样式的多个HTML页面
- 创建包含多个部分的综合文档
