import os
import re
import tkinter as tk
from tkinter import filedialog, messagebox
import webbrowser  # <--- 1. 导入 webbrowser 模块
import pathlib   # <--- 用于更方便地转换为 file URI

# --- 原有的合并逻辑 ---


def merge_html_files(directory, output_filename="merged.html"):
    """
    合并指定目录下的所有 HTML 文件。

    Args:
        directory: 包含 HTML 文件的目录路径。
        output_filename: 合并后输出的文件名。

    Returns:
        str: 合并后文件的完整路径，如果失败则返回 None。
    """
    if not os.path.isdir(directory):
        print(f"错误: 指定的路径不是一个有效的目录: {directory}")
        return None

    html_files = []
    try:
        for filename in os.listdir(directory):
            if filename.endswith(".html") and filename.lower() != output_filename.lower():
                html_files.append(filename)
    except FileNotFoundError:
        print(f"错误: 目录不存在: {directory}")
        return None
    except Exception as e:
        print(f"错误: 读取目录时发生错误: {e}")
        return None

    if not html_files:
        print(f"提示: 在指定目录中未找到需要合并的 HTML 文件 (已排除 {output_filename})。")
        return None

    # 按文件名排序 (自然排序)
    try:
        html_files.sort(key=lambda f: [int(s) if s.isdigit(
        ) else s.lower() for s in re.split(r'(\d+)', f)])
    except Exception as e:
        print(f"警告: 文件自然排序时出现问题 ({e})，将使用字母顺序排序。")
        html_files.sort()

    merged_content = ""
    first_file = True
    # Default head, includes viewport for better mobile viewing
    head_content = "<head>\n<meta charset=\"utf-8\">\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n<title>Merged Document</title>\n</head>"

    print("将按以下顺序合并文件:")
    for fname in html_files:
        print(f"- {fname}")

    for filename in html_files:
        filepath = os.path.join(directory, filename)
        try:
            with open(filepath, "r", encoding="utf-8") as f:
                content = f.read()

                body_match = re.search(
                    r"<body(?:>|\s[^>]*>)(.*?)</body>", content, re.DOTALL | re.IGNORECASE)
                if body_match:
                    body_content = body_match.group(1).strip()

                    if first_file:
                        # 从第一个文件提取完整的head标签
                        head_match = re.search(
                            r"<head(?:>|\s[^>]*>)(.*?)</head>", content, re.DOTALL | re.IGNORECASE)
                        if head_match:
                            head_content = head_match.group(0)
                            print(f"✓ 使用第一个文件 {filename} 的头部样式")
                        else:
                            print(
                                f"⚠ 警告: 第一个文件 {filename} 未找到 <head> 标签，将使用默认头部。")

                        # 从第一个文件提取title，如果存在的话
                        title_match = re.search(
                            r"<title>(.*?)</title>", head_content, re.DOTALL | re.IGNORECASE)
                        if title_match:
                            original_title = title_match.group(1).strip()
                            # 更新title为合并文档的标题
                            head_content = re.sub(
                                r"<title>.*?</title>", f"<title>合并文档 - {original_title}</title>", head_content, flags=re.DOTALL | re.IGNORECASE)
                            print(f"✓ 更新标题为: 合并文档 - {original_title}")

                        first_file = False

                    # 添加文件分隔标记，包含文件名
                    merged_content += f"<!-- 开始: {filename} -->\n"
                    merged_content += body_content
                    merged_content += f"\n<!-- 结束: {filename} -->\n\n<hr style=\"margin: 20px 0; border: 1px solid #ccc;\" />\n\n"

        except FileNotFoundError:
            print(f"警告: 文件 {filepath} 未找到，已跳过。")
            continue
        except Exception as e:
            print(f"警告: 读取或处理文件 {filepath} 时出错: {e}，已跳过。")
            continue

    # Remove the last separator <hr />
    # 移除最后的分隔符
    hr_tag = '<hr style="margin: 20px 0; border: 1px solid #ccc;" />'

    # 查找最后一个hr标签的位置
    last_hr_pos = merged_content.rfind(hr_tag)
    if last_hr_pos != -1:
        # 检查这个hr标签是否在文件末尾附近（允许一些空白字符）
        content_after_hr = merged_content[last_hr_pos + len(hr_tag):].strip()
        if not content_after_hr:  # 如果hr标签后面只有空白字符
            # 移除hr标签及其前面的换行符
            before_hr = merged_content[:last_hr_pos].rstrip()
            merged_content = before_hr
            print("已移除最后的分隔符")
    final_html = f"<!DOCTYPE html>\n<html lang=\"zh-CN\">\n{head_content}\n<body>\n{merged_content}\n</body>\n</html>"

    output_path = os.path.join(directory, output_filename)
    try:
        with open(output_path, "w", encoding="utf-8") as outfile:
            outfile.write(final_html)
        print(f"HTML 文件已成功合并到 {output_path}")
        return output_path  # Return the full path of the merged file
    except Exception as e:
        print(f"错误: 写入合并文件 {output_path} 时出错: {e}")
        return None

# --- GUI 部分 ---


class App:
    def __init__(self, master):
        self.master = master
        master.title("HTML 文件合并工具")
        master.minsize(380, 70)

        self.directory_path = tk.StringVar()
        default_dir = "D:\\test"
        self.directory_path.set(default_dir)

        top_frame = tk.Frame(master)
        top_frame.pack(padx=10, pady=10, fill=tk.X)

        self.label = tk.Label(top_frame, text="HTML 目录:")
        self.label.pack(side=tk.LEFT, padx=(0, 5))

        self.entry = tk.Entry(
            top_frame, textvariable=self.directory_path, width=50)
        self.entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.browse_button = tk.Button(
            top_frame, text="浏览...", command=self.browse_directory)
        self.browse_button.pack(side=tk.LEFT, padx=(5, 0))

        button_frame = tk.Frame(master)
        button_frame.pack(pady=(5, 10))

        self.ok_button = tk.Button(
            button_frame, text="确认合并", command=self.start_merge)
        self.ok_button.pack(side=tk.LEFT, padx=10)

        self.cancel_button = tk.Button(
            button_frame, text="取消", command=master.quit)
        self.cancel_button.pack(side=tk.LEFT, padx=10)

    def browse_directory(self):
        initial_dir = self.directory_path.get()
        if not os.path.isdir(initial_dir):
            # If default or current path is invalid, use home directory
            initial_dir = os.path.expanduser("~")

        selected_directory = filedialog.askdirectory(initialdir=initial_dir)
        if selected_directory:
            self.directory_path.set(selected_directory)
            print(f"用户选择了目录: {selected_directory}")

    def start_merge(self):
        directory = self.directory_path.get()
        if not directory:
            messagebox.showwarning("缺少信息", "请提供一个包含 HTML 文件的目录。")
            return

        if not os.path.isdir(directory):
            messagebox.showerror("路径无效", f"指定的路径不是一个有效的目录:\n{directory}")
            return

        output_filename = "merged.html"
        output_file_path_check = os.path.join(directory, output_filename)

        if os.path.exists(output_file_path_check):
            print(f"注意: 输出文件 {output_filename} 已存在，将被覆盖。")
            # Optionally add user confirmation back here if needed

        try:
            self.ok_button.config(state=tk.DISABLED)
            self.cancel_button.config(state=tk.DISABLED)
            self.master.update()

            output_file_path = merge_html_files(directory, output_filename)

            if output_file_path:
                # 合并成功
                # messagebox.showinfo("合并成功", f"HTML 文件已成功合并到:\n{output_file_path}")

                # --- 2. 添加自动打开文件的代码 ---
                try:
                    # 将普通路径转换为 file:/// URL
                    file_uri = pathlib.Path(
                        os.path.abspath(output_file_path)).as_uri()
                    webbrowser.open_new_tab(file_uri)
                    print(f"尝试在默认浏览器中打开: {file_uri}")
                except Exception as open_err:
                    # 如果自动打开失败，打印错误并显示警告信息
                    print(f"错误: 无法自动打开文件 '{output_file_path}': {open_err}")
                    messagebox.showwarning("打开文件失败",
                                           f"合并成功，但无法自动在浏览器中打开文件。\n"
                                           f"请手动打开:\n{output_file_path}",
                                           parent=self.master)  # 指定父窗口
                # ------------------------------------

            else:
                # 合并失败
                if os.path.isdir(directory) and not any(f.endswith(".html") and f.lower() != output_filename.lower() for f in os.listdir(directory)):
                    messagebox.showinfo(
                        "提示", f"在指定目录中未找到需要合并的 HTML 文件 (已排除 {output_filename})。")
                else:
                    messagebox.showerror("合并失败", "合并过程中发生错误，请查看控制台输出获取详细信息。")

        except Exception as e:
            messagebox.showerror("意外错误", f"执行合并时发生意外错误:\n{e}")
        finally:
            # 恢复按钮状态
            self.ok_button.config(state=tk.NORMAL)
            self.cancel_button.config(state=tk.NORMAL)


# --- 主程序入口 ---
if __name__ == "__main__":
    root = tk.Tk()
    app = App(root)
    root.mainloop()
